using Castle.DynamicProxy;
using Core.Cache.Abstract;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache Aspect - Method sonuçlarını cache'ler
    /// Multi-tenant yapıya uygun, Redis tabanlı cache aspect
    /// </summary>
    public class CacheAspect : MethodInterception
    {
        private int _duration;
        private IRedisService _redisService;
        private ICacheKeyGenerator _keyGenerator;

        public CacheAspect(int duration = 60)
        {
            _duration = duration;
            _redisService = ServiceTool.ServiceProvider.GetService<IRedisService>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Company ID'yi context'ten al
            var companyId = GetCompanyIdFromContext();
            
            // Cache key oluştur
            var methodName = string.Format($"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}");
            var arguments = invocation.Arguments.ToList();
            
            // Method bazlı cache key
            var cacheKey = _keyGenerator.GenerateMethodKey(
                companyId, 
                invocation.Method.ReflectedType.Name, 
                invocation.Method.Name, 
                invocation.Arguments);

            // Cache'den veri al
            var cacheResult = _redisService.GetAsync<object>(cacheKey).Result;
            
            if (cacheResult.Success && cacheResult.IsFromCache)
            {
                // Cache hit - cached değeri döndür
                invocation.ReturnValue = cacheResult.Data;
                return;
            }

            // Cache miss - method'u çalıştır
            invocation.Proceed();

            // Sonucu cache'e kaydet
            if (invocation.ReturnValue != null)
            {
                _redisService.SetAsync(cacheKey, invocation.ReturnValue, _duration).Wait();
            }
        }

        private int GetCompanyIdFromContext()
        {
            try
            {
                // HttpContext'ten company ID al
                var httpContextAccessor = ServiceTool.ServiceProvider.GetService<Microsoft.AspNetCore.Http.IHttpContextAccessor>();
                var httpContext = httpContextAccessor?.HttpContext;
                
                if (httpContext?.User?.Identity?.IsAuthenticated == true)
                {
                    var companyIdClaim = httpContext.User.Claims.FirstOrDefault(c => c.Type == "CompanyId");
                    if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out var companyId))
                    {
                        return companyId;
                    }
                }
                
                // Fallback: Default company ID
                return 1;
            }
            catch
            {
                // Hata durumunda default company ID
                return 1;
            }
        }
    }
}
